# Resume Optimizer - ATS Analysis Tool

A professional-grade application to help job seekers optimize their resumes for Applicant Tracking Systems (ATS), powered by NLP analysis.

## Features

- **Resume Analysis**: Parse and analyze resumes in PDF and DOCX formats
- **ATS Scoring**: Get a compatibility score based on job description matching
- **Skill Gap Analysis**: Identify missing skills from job descriptions
- **Improvement Recommendations**: Get actionable advice to improve your resume
- **Fast & Efficient**: Optimized for large documents with async processing
- **Secure**: Local processing with optional rate limiting

## Table of Contents

- [API Documentation](#api-documentation)
- [Setup Instructions](#setup-instructions)
- [Development](#development)
- [Architecture](#architecture)
- [Configuration](#configuration)
- [Contributing](#contributing)
- [License](#license)

## API Documentation

### Base URL
```
http://localhost:8000
```

### Endpoints

#### 1. Health Check

```http
GET /health
```

Check the health status of the API.

**Responses:**
- `200 OK`: Service is healthy
  ```json
  {
    "status": "healthy",
    "timestamp": **********.789,
    "version": "1.0.0",
    "components": {
      "api": true,
      "rate_limiting": {
        "enabled": true,
        "limit": 100,
        "window_seconds": 60
      },
      "file_processing": true
    }
  }
  ```

#### 2. Process Resume

```http
POST /process
```

Process a resume and optionally compare it with a job description.

**Request Body (multipart/form-data):**
- `resume`: (required) Resume file (PDF or DOCX, max 5MB)
- `jd_text`: (optional) Job description text

**Responses:**
- `200 OK`: Analysis completed successfully
  ```json
  {
    "success": true,
    "jd_text": "Job description text...",
    "resume_structure": { ... },
    "resume_text": "Extracted resume text...",
    "ats_score": 85.5,
    "suggested_skills": ["Python", "Machine Learning"],
    "improvement_recommendation": [
      {
        "issue": "Missing summary section",
        "advice": "Add a professional summary at the top of your resume..."
      }
    ]
  }
  ```
- `400 Bad Request`: Invalid input or file format
- `413 Payload Too Large`: File exceeds maximum size
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

#### 2. Health Check

```http
GET /health
```

Check if the API is running and healthy.

**Responses:**
- `200 OK`: Service is healthy
  ```json
  {
    "status": "healthy",
    "timestamp": **********.123456
  }
  ```

## Setup Instructions

### Prerequisites

- Python 3.8+
- pip
- (Optional) Virtual environment

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/resume-optimizer.git
   cd resume-optimizer
   ```

2. **Set up Python environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**
   Create a `.env` file in the project root:
   ```env
   DEBUG=true
   LOG_LEVEL=info
   MAX_FILE_SIZE=5242880  # 5MB
   ```

5. **Run the application**
```bash
# Install test dependencies
pip install -r requirements-test.txt
python -m spacy download en_core_web_sm
cd backend
uvicorn main:app --reload
   ```


## Development

### Project Structure

```bash
backend/
├── __init__.py
├── main.py              # FastAPI application
├── async_utils.py       # Async processing utilities
├── parser.py            # Resume parsing logic
├── ats_calculator.py    # ATS scoring logic
├── restructure_advice.py # Resume improvement suggestions
└── requirements.txt     # Python dependencies

frontend/                # Chrome extension files
├── popup.js
├── content.js
├── background.js
└── ...
```





## Configuration

All configuration is handled through environment variables. See `config.py` for all available options.

### Rate Limiting

The API is rate limited to 10 requests per minute per IP address by default. This can be configured using:

```env
RATE_LIMIT=10        # Requests per minute
RATE_LIMIT_WINDOW=60 # Time window in seconds
```

### File Uploads

- **Allowed Extensions**: PDF, DOCX, DOC
- **Max File Size**: 5MB (configurable via `MAX_FILE_SIZE`)

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request



---

