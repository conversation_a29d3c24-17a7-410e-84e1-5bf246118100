from skillNer.skill_extractor_class import SkillExtractor
from skillNer.general_params import SKILL_DB
import spacy
import re
from typing import List, Set, Dict, Any

# Load spaCy and SkillNER
nlp = spacy.load("en_core_web_lg")
skill_extractor = SkillExtractor(nlp, SKILL_DB, PhraseMatcher=spacy.matcher.PhraseMatcher)

def clean_skill(skill: str) -> str:
    """Clean and normalize skill text."""
    return skill.lower().strip()

def extract_skills(text: str) -> Set[str]:
    """
    Extract skills from text using SkillNER and pattern matching.
    Returns a set of cleaned and normalized skill names.
    """
    if not text or not isinstance(text, str):
        return set()
    
    skills = set()
    
    # Extract skills using SkillNER
    try:
        annotations = skill_extractor.annotate(text)
        # Get full matches
        full_matches = {clean_skill(match['doc_node_value']) 
                       for match in annotations['results']['full_matches']}
        # Get n-gram matches with confidence > 0.7
        ngram_matches = {clean_skill(match['doc_node_value'])
                        for match in annotations['results']['ngram_scored']
                        if match.get('score', 0) > 0.7}
        skills.update(full_matches.union(ngram_matches))
    except Exception as e:
        print(f"SkillNER extraction error: {e}")
    
    # Also look for common skill patterns
    skill_patterns = [
        r'\b(?:proficient in|experience with|skilled in|expertise in|knowledge of|familiar with)[\s:]+([\w\s/&+.-]+)(?:\.|,|;|$)',
        r'\b(?:technologies?|skills?|tools?)[\s:]+([\w\s/&+.-]+)(?:\.|,|;|$)'
    ]
    
    for pattern in skill_patterns:
        for match in re.finditer(pattern, text, re.IGNORECASE):
            skills.update(clean_skill(s) for s in re.split(r'[,/&+]', match.group(1)) if s.strip())
    
    return skills

def get_missing_skills(jd_text: str, resume_text: str) -> List[str]:
    """
    Returns a list of skills present in the job description but missing from the resume.
    """
    if not jd_text or not resume_text:
        return []  # Return empty list when inputs are invalid
    
    jd_skills = extract_skills(jd_text)
    resume_skills = extract_skills(resume_text)
    
    # Find skills in JD that aren't in resume
    missing_skills = jd_skills - resume_skills
    
    # Sort by relevance
    return sorted(list(missing_skills))

    

def get_skill_analysis(jd_text: str, resume_text: str, resume_structure: List[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Generate skill analysis including missing skills and suggestions.
    """
    if not jd_text or not resume_text:
        return {"error": "Missing job description or resume text"}
    
    try:
        missing_skills = get_missing_skills(jd_text, resume_text)
        


        if missing_skills:
            skills_list = ", ".join(missing_skills[:-1])
            if len(missing_skills) > 1:
                skills_list += f" and {missing_skills[-1]}"
            else:
                skills_list = missing_skills[0]

        
        return {
            "missing_skills": missing_skills
            
        }
    except Exception as e:
        return {"error": f"Error analyzing skills: {str(e)}"}