<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>About - NLP Resume Optimizer Extension</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="icon" type="image/png" href="icons/icon32.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
</head>
<body class="bg-white min-h-screen text-gray-700 font-sans p-8">
  <div class="max-w-xl mx-auto">
    <h1 class="text-2xl font-bold mb-2">NLP Resume Optimizer</h1>
    <p class="mb-4">Chrome Extension <span class="text-sm bg-green-100 text-green-700 rounded px-2 py-0.5">Phase 1</span></p>
    <h2 class="text-xl font-semibold mt-5 mb-2">Tech Stack</h2>
    <ul class="list-disc pl-6 space-y-1 text-base">
      <li><b>Chrome Extension</b>: Manifest V3, permissions: activeTab, storage, scripting, host: <span class="font-mono">https://www.linkedin.com/*</span></li>
      <li><b>Frontend</b>: React (via CDN, no build tools), Tailwind CSS (CDN)</li>
      <li><b>Popup ↔ Content Scripts communication</b>: chrome.tabs and chrome.runtime messaging</li>
      <li><b>Job Description Extraction:</b> DOM scanning, links to "About this job" section</li>
      <li><b>PDF Resume Upload:</b> metadata only (filename, size, type)</li>
      <li><b>UX:</b> Loading states, strong visual hierarchy, full error handling</li>
    </ul>
    <h2 class="font-semibold text-lg mt-6 mb-2">Architecture Overview</h2>
    <ol class="list-decimal pl-6 space-y-1">
      <li>
        <b>User Interface (popup.html):</b> Renders React app, shows scan/upload/results, communicates via chrome.tabs to the page (content.js)
      </li>
      <li>
        <b>Content Script (content.js):</b> Injected on LinkedIn, extracts live job description text for analysis.
      </li>
      <li>
        <b>PDF Handling:</b> Operates purely client-side; no resume is uploaded, only metadata shown. //later add functionality to store resume in db
      </li>
      <li>
        <b>Security:</b> No remote scripts, Tailwind+React via official CDN only, minimal permissions.
      </li>
    </ol>
    <hr class="my-6"/>
    <h2 class="font-semibold text-lg">Future Plans</h2>
    <ul class="list-disc pl-6 text-base">
      <li>Python-powered keyword extraction (ML)</li>
      <li>Comprehensive resume feedback and actionable recommendations</li>
      <li>Skill gap and ATS score calculation</li>
      <li>Support for R and analytics viz, MongoDB/SQL storage, and much more</li>
    </ul>
    <hr class="my-6"/>
    <p class="text-xs text-gray-400">Built for educational purposes. Not affiliated with LinkedIn.</p>
  </div>
</body>
</html>
